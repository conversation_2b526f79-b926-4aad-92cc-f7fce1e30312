@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo    Conversor de Audio 5.1 para 2.0 (Estereo)
echo    Qualidade Maxima Sem Compressao
echo ===================================================
echo.

REM Verificar se o FFmpeg está instalado
where ffmpeg >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: FFmpeg nao encontrado. Verifique se esta instalado e no PATH.
    echo Caminho esperado: C:\ffmpeg\bin\ffmpeg.exe
    echo.
    echo Pressione qualquer tecla para sair...
    pause >nul
    exit /b 1
)

REM Definir caminhos (usando o diretório atual como base)
set "audio_folder=%~dp0audio"
set "output_folder=%~dp0audio\convertido"

REM Verificar se as pastas existem
if not exist "%audio_folder%" (
    echo Criando pasta de audio: %audio_folder%
    mkdir "%audio_folder%"
)

if not exist "%output_folder%" (
    echo Criando pasta de saida: %output_folder%
    mkdir "%output_folder%"
)

REM Verificar se há arquivos de áudio na pasta
set "found_audio=0"
for %%F in ("%audio_folder%\*.mka" "%audio_folder%\*.ac3" "%audio_folder%\*.dts" "%audio_folder%\*.eac3" "%audio_folder%\*.aac" "%audio_folder%\*.mp3" "%audio_folder%\*.flac" "%audio_folder%\*.wav") do (
    if exist "%%F" set "found_audio=1"
)

if %found_audio%==0 (
    echo.
    echo Nenhum arquivo de audio encontrado na pasta de audio.
    echo.
    set /p "audio_path=Digite o caminho completo do arquivo de audio 5.1: "
    
    if not exist "!audio_path!" (
        echo ERRO: Arquivo nao encontrado: !audio_path!
        echo.
        echo Pressione qualquer tecla para sair...
        pause >nul
        exit /b 1
    )
    
    REM Extrair nome do arquivo
    for %%F in ("!audio_path!") do set "audio_filename=%%~nxF"
    
    REM Copiar arquivo para a pasta de áudio
    echo.
    echo Copiando arquivo para a pasta de audio...
    copy "!audio_path!" "%audio_folder%\!audio_filename!" > nul
    
    set "audio_file=%audio_folder%\!audio_filename!"
) else (
    REM Listar arquivos de áudio disponíveis
    echo Arquivos de audio disponiveis:
    echo.
    
    set "file_count=0"
    for %%F in ("%audio_folder%\*.mka" "%audio_folder%\*.ac3" "%audio_folder%\*.dts" "%audio_folder%\*.eac3" "%audio_folder%\*.aac" "%audio_folder%\*.mp3" "%audio_folder%\*.flac" "%audio_folder%\*.wav") do (
        if exist "%%F" (
            set /a "file_count+=1"
            set "file_!file_count!=%%F"
            set "filename_!file_count!=%%~nxF"
            echo !file_count!. %%~nxF
        )
    )
    
    echo.
    set /p "selected_file=Selecione o numero do arquivo (1-%file_count%): "
    
    REM Validar seleção
    if !selected_file! LSS 1 (
        echo ERRO: Selecao invalida.
        echo.
        echo Pressione qualquer tecla para sair...
        pause >nul
        exit /b 1
    )
    
    if !selected_file! GTR %file_count% (
        echo ERRO: Selecao invalida.
        echo.
        echo Pressione qualquer tecla para sair...
        pause >nul
        exit /b 1
    )
    
    REM Definir o arquivo de áudio selecionado
    set "audio_file=!file_%selected_file%!"
    set "audio_filename=!filename_%selected_file%!"
    
    echo.
    echo Arquivo selecionado: !audio_filename!
    echo Caminho completo: !audio_file!
    echo.
)

REM Definir nome do arquivo de saída
for %%F in ("%audio_file%") do set "output_basename=%%~nF"
set "output_file=%output_folder%\%output_basename%_stereo.wav"

echo.
echo Iniciando conversao de audio 5.1 para 2.0 (estereo)...
echo Usando metodo de qualidade maxima sem compressao...
echo.

REM Usar o método de qualidade máxima com filtro personalizado
ffmpeg -i "%audio_file%" -af "pan=stereo|FL=0.5*FC+0.707*FL+0.707*BL+0.5*LFE|FR=0.5*FC+0.707*FR+0.707*BR+0.5*LFE" -c:a pcm_s24le "%output_file%"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERRO: Falha ao converter o audio.
    echo.
    echo Pressione qualquer tecla para sair...
    pause >nul
    exit /b 1
)

echo.
echo ===================================================
echo Audio convertido com sucesso!
echo.
echo Arquivo de audio original: %audio_file%
echo Arquivo de audio convertido: %output_file%
echo ===================================================
echo.

echo Pressione qualquer tecla para sair...
pause >nul
exit /b 0

