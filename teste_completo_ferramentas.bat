@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   TESTE COMPLETO DE FERRAMENTAS
echo ===================================================
echo.

set "tools_ok=0"

echo 1. Testando FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] FFmpeg funcionando!
    set /a tools_ok+=1
) else (
    echo [ERRO] FFmpeg nao encontrado!
)

echo.
echo 2. Testando MediaInfo CLI...
"C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe" --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] MediaInfo CLI funcionando!
    echo Versao: 
    "C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe" --version 2>&1 | findstr "MediaInfo"
    set /a tools_ok+=1
) else (
    echo [ERRO] MediaInfo CLI nao encontrado!
)

echo.
echo 3. Testando MKVToolNix...
mkvextract >nul 2>&1
if %errorlevel% neq 9009 (
    echo [OK] MKVToolNix funcionando!
    set /a tools_ok+=1
) else (
    echo [ERRO] MKVToolNix nao encontrado!
)

echo.
echo ===================================================
echo RESULTADO: !tools_ok! de 3 ferramentas funcionando
echo ===================================================

if !tools_ok! equ 3 (
    echo.
    echo [SUCESSO] Todas as ferramentas estao funcionando!
    echo O script converter_audio_final.bat deve funcionar perfeitamente.
    echo.
    echo PROXIMOS PASSOS:
    echo 1. Coloque um arquivo .mkv na pasta 'filmes'
    echo 2. Execute o converter_audio_final.bat
    echo 3. Escolha o tipo de conversao desejada
) else (
    echo.
    echo [ATENCAO] Algumas ferramentas nao estao funcionando.
    if !tools_ok! geq 2 (
        echo Mas o script ainda pode funcionar com limitacoes.
    ) else (
        echo Instale as ferramentas em falta antes de continuar.
    )
)

echo.
pause
