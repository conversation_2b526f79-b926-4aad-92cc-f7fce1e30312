@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   Extracao e Conversao de Audio para o Kodi
echo   Com suporte a Dolby Pro Logic II e Voz clara
echo ===================================================
echo.

REM Verificar ferramentas necessarias
where ffmpeg >nul 2>&1 || (echo FFmpeg nao encontrado. && pause && exit /b)
where MediaInfo >nul 2>&1 || (echo MediaInfo nao encontrado. && pause && exit /b)
where mkvextract >nul 2>&1 || (echo MKVToolNix (mkvextract) nao encontrado. && pause && exit /b)

REM Pasta dos arquivos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"

if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"

echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. %%~nxF
)

if "!i!"=="0" (
    echo Nenhum arquivo encontrado.
    pause
    exit /b
)

set /p "sel=Escolha o numero do arquivo: "

REM Validar entrada do usuario
if "!sel!"=="" (
    echo ERRO: Nenhuma opcao selecionada.
    pause
    exit /b
)

REM Verificar se a selecao e um numero valido
set "valid=0"
for /l %%n in (1,1,!i!) do (
    if "!sel!"=="%%n" set "valid=1"
)

if "!valid!"=="0" (
    echo ERRO: Opcao invalida. Escolha um numero entre 1 e !i!.
    pause
    exit /b
)

set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

REM Nome base para os arquivos convertidos
set /p "movie_base=Digite o nome base do filme (ex: filme_suspense_2024): "

echo.
echo Detectando faixas de audio em: !selected_filename!
MediaInfo --Output=JSON "!selected_file!" > "%base_folder%mediainfo.json"

REM Extrair indice da faixa de audio em portugues
echo Analisando faixas de audio...
set "audio_track_index="
set "track_count=0"

REM Listar todas as faixas de audio disponiveis
echo.
echo Faixas de audio encontradas:
for /f "tokens=1,2 delims=," %%a in ('MediaInfo --Inform^="Audio;%%StreamOrder%%,%%Language%%\n" "!selected_file!"') do (
    set /a track_count+=1
    echo Faixa !track_count!: Idioma %%b (ID: %%a)
    if /i "%%b"=="pt" set "audio_track_index=%%a"
    if /i "%%b"=="por" set "audio_track_index=%%a"
    if /i "%%b"=="portuguese" set "audio_track_index=%%a"
)

:found
if not defined audio_track_index (
    echo.
    echo ERRO: Nenhuma faixa em portugues encontrada.
    echo Faixas disponiveis foram listadas acima.
    echo Verifique se o arquivo possui audio em portugues.
    pause
    exit /b
)

echo.
echo Faixa em portugues encontrada no indice: !audio_track_index!

REM Extrair audio
set "audio_output=%audio_folder%\!movie_base!_orig.mka"
echo.
echo Extraindo faixa de audio...
mkvextract tracks "!selected_file!" !audio_track_index!:"!audio_output!"

if not exist "!audio_output!" (
    echo ERRO: Falha na extracao de audio.
    echo Verifique se o MKVToolNix esta instalado corretamente.
    pause
    exit /b
)

echo Audio extraido com sucesso: !audio_output!

echo.
echo Deseja converter para:
echo 1. Apenas Dolby Pro Logic II
echo 2. Apenas Voz mais clara
echo 3. Ambas as versoes
set /p "convert_mode=Opcao (1-3): "

REM Validar modo de conversao
if not "!convert_mode!"=="1" if not "!convert_mode!"=="2" if not "!convert_mode!"=="3" (
    echo ERRO: Opcao invalida. Escolha 1, 2 ou 3.
    pause
    exit /b
)

REM Definir os filtros (melhorados para compatibilidade com Kodi)
set "filter_dolby=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
set "filter_voz=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"

echo.
echo Iniciando conversao...

REM Converter de acordo com a escolha
if "!convert_mode!"=="1" (
    echo Convertendo para Dolby Pro Logic II...
    ffmpeg -i "!audio_output!" -af "!filter_dolby!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_dolby.flac"
    if !errorlevel! neq 0 (
        echo ERRO: Falha na conversao Dolby Pro Logic II.
        pause
        exit /b
    )
    echo Conversao Dolby Pro Logic II concluida.
)

if "!convert_mode!"=="2" (
    echo Convertendo para Voz mais clara...
    ffmpeg -i "!audio_output!" -af "!filter_voz!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_voz.flac"
    if !errorlevel! neq 0 (
        echo ERRO: Falha na conversao Voz clara.
        pause
        exit /b
    )
    echo Conversao Voz clara concluida.
)

if "!convert_mode!"=="3" (
    echo Convertendo para Dolby Pro Logic II...
    ffmpeg -i "!audio_output!" -af "!filter_dolby!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_dolby.flac"
    if !errorlevel! neq 0 (
        echo ERRO: Falha na conversao Dolby Pro Logic II.
        pause
        exit /b
    )
    echo Conversao Dolby Pro Logic II concluida.
    
    echo Convertendo para Voz mais clara...
    ffmpeg -i "!audio_output!" -af "!filter_voz!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_voz.flac"
    if !errorlevel! neq 0 (
        echo ERRO: Falha na conversao Voz clara.
        pause
        exit /b
    )
    echo Conversao Voz clara concluida.
)

REM Limpeza do arquivo temporario
if exist "!audio_output!" (
    echo.
    set /p "cleanup=Deseja remover o arquivo de audio temporario? (s/n): "
    if /i "!cleanup!"=="s" (
        del "!audio_output!"
        echo Arquivo temporario removido.
    )
)

REM Limpeza do arquivo JSON do MediaInfo
if exist "%base_folder%mediainfo.json" del "%base_folder%mediainfo.json"

echo.
echo ===================================================
echo Conversao concluida com sucesso!
echo Arquivos estao em: "!convertido_folder!"
echo Nomes prontos para o Kodi reconhecer.
echo.
echo DICA: Para usar no Kodi, coloque os arquivos .flac
echo na mesma pasta do filme com o mesmo nome base.
echo ===================================================
pause
exit /b
