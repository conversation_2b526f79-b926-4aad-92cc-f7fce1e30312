@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   Conversor de Audio 5.1 para 2.0 (Estereo)
echo   FLAC sem perdas + Nome personalizado p/ KODI
echo ===================================================
echo.

REM Caminho fixo para o MediaInfo CLI
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"

REM Verificar se o FFmpeg esta instalado
where ffmpeg >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: FFmpeg nao encontrado. Verifique se esta instalado e no PATH.
    pause >nul
    exit /b 1
)

REM Verificar se o MediaInfo esta acessível
if not exist "%mediainfo_cli%" (
    echo ERRO: MediaInfo CLI nao encontrado no caminho:
    echo %mediainfo_cli%
    pause >nul
    exit /b 1
)

REM Definir pastas
set "audio_folder=%~dp0audio"
set "output_folder=%~dp0audio\convertido"

if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%output_folder%" mkdir "%output_folder%"

REM Pedir nome base do filme
echo.
set /p "movie_name=Digite o nome base do filme (ex: MeuFilme.2024.1080p): "

echo.
echo Arquivos de audio encontrados:
set "file_count=0"
for %%F in ("%audio_folder%\*.mka" "%audio_folder%\*.ac3" "%audio_folder%\*.dts" "%audio_folder%\*.eac3" "%audio_folder%\*.aac" "%audio_folder%\*.mp3" "%audio_folder%\*.flac" "%audio_folder%\*.wav") do (
    if exist "%%F" (
        set /a file_count+=1
        set "file_!file_count!=%%F"
        set "filename_!file_count!=%%~nxF"
        echo !file_count!. %%~nxF
    )
)

if %file_count%==0 (
    echo Nenhum arquivo encontrado em "%audio_folder%"
    pause >nul
    exit /b
)

set /p "selected_file=Selecione o numero do arquivo (1-%file_count%): "
set "audio_file=!file_%selected_file%!"
set "audio_filename=!filename_%selected_file%!"

REM Detectar número de canais com MediaInfo
for /f %%C in ('"%mediainfo_cli%" --Inform^="Audio;%%Channel(s)%%" "!audio_file!"') do set "channels=%%C"

echo.
echo Arquivo selecionado: !audio_filename!
echo Canais detectados: !channels!
echo.

if %channels% LSS 5 (
    echo Audio ja estereo. Conversao nao necessaria.
    pause >nul
    exit /b
)

REM Modos de conversao
set "pan_dolby=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
set "pan_voz=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"

set "out_dolby=%output_folder%\%movie_name%.por.DolbyProLogicII.flac"
set "out_voz=%output_folder%\%movie_name%.por.VozClara.flac"

echo Iniciando conversoes...

echo.
echo >> [1/2] Modo Dolby Pro Logic II
ffmpeg -y -i "!audio_file!" -af "%pan_dolby%" -c:a flac "!out_dolby!"
if %ERRORLEVEL% NEQ 0 echo ERRO na conversao Dolby!

echo.
echo >> [2/2] Modo Voz Mais Clara
ffmpeg -y -i "!audio_file!" -af "%pan_voz%" -c:a flac "!out_voz!"
if %ERRORLEVEL% NEQ 0 echo ERRO na conversao Voz!

echo.
echo ===================================================
echo Conversoes concluidas.
echo Arquivos gerados:
echo !out_dolby!
echo !out_voz!
echo ===================================================
pause >nul
exit /b
