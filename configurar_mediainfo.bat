@echo off
echo ===================================================
echo   CONFIGURANDO MEDIAINFO CLI
echo ===================================================
echo.

echo Verificando se a pasta do MediaInfo CLI existe...
if exist "C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe" (
    echo [OK] MediaInfo CLI encontrado em: C:\MediaInfo_CLI_25.04_Windows_x64\
    echo.
    
    echo Testando MediaInfo CLI...
    "C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe" --version
    
    if %errorlevel% equ 0 (
        echo.
        echo [SUCESSO] MediaInfo CLI esta funcionando!
        echo.
        echo OPCOES:
        echo 1. Adicionar ao PATH do sistema (recomendado)
        echo 2. Usar caminho completo no script
        echo.
        set /p "opcao=Escolha uma opcao (1 ou 2): "
        
        if "!opcao!"=="1" (
            echo.
            echo Adicionando ao PATH do sistema...
            setx PATH "%PATH%;C:\MediaInfo_CLI_25.04_Windows_x64" /M 2>nul
            if %errorlevel% equ 0 (
                echo [OK] Adicionado ao PATH com sucesso!
                echo REINICIE o prompt de comando para usar.
            ) else (
                echo [AVISO] Nao foi possivel adicionar automaticamente.
                echo Execute como Administrador ou adicione manualmente.
            )
        )
        
        if "!opcao!"=="2" (
            echo.
            echo Criando versao do script com caminho completo...
            echo Script sera modificado para usar o caminho completo.
        )
    ) else (
        echo [ERRO] MediaInfo CLI nao esta funcionando corretamente.
    )
) else (
    echo [ERRO] MediaInfo CLI nao encontrado em: C:\MediaInfo_CLI_25.04_Windows_x64\
    echo Verifique se o caminho esta correto.
)

echo.
pause
