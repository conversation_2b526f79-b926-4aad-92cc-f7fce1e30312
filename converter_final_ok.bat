@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONVERSOR DE AUDIO FINAL - VERSAO OK
echo   Aceita espacos e nao fecha
echo ===================================================
echo.

REM Caminhos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "convertido_folder=%base_folder%audio\convertido"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mkvextract_exe=C:\MKVToolNix\mkvextract.exe"

REM Criar pastas
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"

echo Verificando ferramentas...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] FFmpeg nao encontrado.
    pause
    exit /b 1
)

if not exist "%mkvextract_exe%" (
    echo [ERRO] MKVToolNix nao encontrado.
    pause
    exit /b 1
)

if not exist "%mediainfo_cli%" (
    echo [ERRO] MediaInfo CLI nao encontrado.
    pause
    exit /b 1
)

echo [OK] Todas as ferramentas encontradas.
echo.

echo Coloque o arquivo .mkv na pasta: "%input_folder%"
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "count=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a count+=1
    set "arquivo_!count!=%%F"
    set "nome_!count!=%%~nxF"
    echo !count!. %%~nxF
)

if %count%==0 (
    echo Nenhum arquivo encontrado.
    pause
    exit /b 1
)

REM Selecionar arquivo
echo.
if %count%==1 (
    echo Usando o unico arquivo encontrado.
    set "escolha=1"
) else (
    set /p "escolha=Digite o numero do arquivo: "
    if not defined escolha set "escolha=1"
)

set "arquivo_selecionado=!arquivo_%escolha%!"
set "nome_selecionado=!nome_%escolha%!"

echo.
echo Arquivo selecionado: !nome_selecionado!

REM Nome do filme (simplificado)
echo.
echo Digite o nome do filme:
set /p "nome_filme=Nome: "

if not defined nome_filme (
    echo Usando nome padrao: filme
    set "nome_filme=filme"
)

REM Limpar nome (metodo mais seguro)
set "nome_limpo=!nome_filme!"
set "nome_limpo=!nome_limpo: =_!"

echo Nome do arquivo: !nome_limpo!

REM Analisar audio
echo.
echo Analisando faixas de audio...
"%mediainfo_cli%" --Inform="Audio;%%ID%% - %%Language%% - %%Format%% - %%Channels%% canais\n" "!arquivo_selecionado!" > temp_info.txt 2>nul

if exist temp_info.txt (
    echo.
    echo Faixas disponiveis:
    type temp_info.txt
    echo.
    del temp_info.txt
) else (
    echo Erro ao analisar. Usando faixa padrao.
)

REM Selecionar faixa
set /p "faixa=Digite o numero da faixa (Enter = faixa 1): "
if not defined faixa set "faixa=1"

REM Extrair audio
echo.
echo Extraindo audio da faixa !faixa!...
set "audio_temp=!nome_limpo!_temp.mka"
"%mkvextract_exe%" tracks "!arquivo_selecionado!" !faixa!:"!audio_temp!" >nul 2>&1

if not exist "!audio_temp!" (
    echo Tentando faixa 0...
    "%mkvextract_exe%" tracks "!arquivo_selecionado!" 0:"!audio_temp!" >nul 2>&1
)

if not exist "!audio_temp!" (
    echo ERRO: Nao foi possivel extrair audio.
    pause
    exit /b 1
)

echo Audio extraido com sucesso.

REM Modo de conversao
echo.
echo Escolha o modo:
echo 1. Dolby Pro Logic II
echo 2. Voz mais clara
echo 3. Ambas as versoes
echo 4. Apenas FLAC
echo.
set /p "modo=Modo (Enter = modo 4): "
if not defined modo set "modo=4"

echo.
echo Convertendo...

REM Executar conversao
if "!modo!"=="1" (
    echo Criando Dolby Pro Logic II...
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR" -c:a flac -compression_level 8 "%convertido_folder%\!nome_limpo!-pt-BR_dolby.flac" -y >nul 2>&1
    echo Dolby criado.
)

if "!modo!"=="2" (
    echo Criando Voz clara...
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR" -c:a flac -compression_level 8 "%convertido_folder%\!nome_limpo!-pt-BR_voz.flac" -y >nul 2>&1
    echo Voz criada.
)

if "!modo!"=="3" (
    echo Criando Dolby Pro Logic II...
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR" -c:a flac -compression_level 8 "%convertido_folder%\!nome_limpo!-pt-BR_dolby.flac" -y >nul 2>&1
    
    echo Criando Voz clara...
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR" -c:a flac -compression_level 8 "%convertido_folder%\!nome_limpo!-pt-BR_voz.flac" -y >nul 2>&1
    
    echo Ambas criadas.
)

if "!modo!"=="4" (
    echo Criando FLAC original...
    ffmpeg -i "!audio_temp!" -c:a flac -compression_level 8 "%convertido_folder%\!nome_limpo!-pt-BR_original.flac" -y >nul 2>&1
    echo FLAC criado.
)

REM Limpeza
if exist "!audio_temp!" del "!audio_temp!"

echo.
echo ===================================================
echo CONVERSAO CONCLUIDA!
echo ===================================================
echo.
echo Arquivos em: %convertido_folder%
echo.
echo COMO USAR NO KODI:
echo 1. Coloque os .flac na pasta do filme
echo 2. Use o mesmo nome base do video
echo 3. Kodi reconhecera automaticamente
echo.

pause
