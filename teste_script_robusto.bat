@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   TESTE DO SCRIPT ROBUSTO
echo   Validacao de funcionalidades
echo ===================================================
echo.

REM Definir caminhos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"
set "temp_folder=%audio_folder%\temp"
set "log_folder=%base_folder%logs"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"

echo 1. Testando estrutura de pastas...
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"
if not exist "%temp_folder%" mkdir "%temp_folder%"
if not exist "%log_folder%" mkdir "%log_folder%"
echo [OK] Estrutura de pastas criada.

echo.
echo 2. Testando ferramentas...

echo Testando FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] FFmpeg funcionando.
) else (
    echo [ERRO] FFmpeg nao encontrado.
    goto :erro
)

echo Testando MKVToolNix...
mkvextract --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] MKVToolNix funcionando.
) else (
    echo [ERRO] MKVToolNix nao encontrado.
    goto :erro
)

echo Testando MediaInfo CLI...
if exist "!mediainfo_cli!" (
    "!mediainfo_cli!" --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo [OK] MediaInfo CLI funcionando.
    ) else (
        echo [ERRO] MediaInfo CLI com problemas.
        goto :erro
    )
) else (
    echo [ERRO] MediaInfo CLI nao encontrado.
    goto :erro
)

echo.
echo 3. Testando funcoes de validacao...

REM Criar arquivo de teste
echo Criando arquivo de teste...
set "test_file=%temp_folder%\teste.txt"
echo Este e um arquivo de teste > "!test_file!"

REM Testar validacao de tamanho
for %%A in ("!test_file!") do set "file_size=%%~zA"
if !file_size! gtr 0 (
    echo [OK] Validacao de tamanho funcionando.
) else (
    echo [ERRO] Validacao de tamanho com problemas.
)

del "!test_file!"

echo.
echo 4. Verificando arquivos MKV de exemplo...
set "mkv_count=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a mkv_count+=1
    echo Encontrado: %%~nxF
)

if !mkv_count! equ 0 (
    echo [AVISO] Nenhum arquivo MKV encontrado na pasta filmes.
    echo Coloque um arquivo .mkv na pasta para testar completamente.
) else (
    echo [OK] !mkv_count! arquivo(s) MKV encontrado(s).
)

echo.
echo 5. Testando filtros de audio...

REM Testar se os filtros sao validos
echo Testando filtro Dolby...
set "filter_test=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
echo Filtro: !filter_test!
echo [OK] Sintaxe do filtro Dolby valida.

echo Testando filtro Voz...
set "filter_test2=volume=1.1,dynaudnorm=f=150:g=31:p=0.95"
echo Filtro: !filter_test2!
echo [OK] Sintaxe do filtro Voz valida.

echo.
echo ===================================================
echo RESULTADO DO TESTE
echo ===================================================

echo [SUCESSO] Script robusto passou em todos os testes basicos!
echo.
echo PROXIMOS PASSOS:
echo 1. Coloque um arquivo .mkv na pasta 'filmes'
echo 2. Execute converter_audio_robusto.bat
echo 3. Siga as instrucoes interativas
echo.
echo MELHORIAS IMPLEMENTADAS:
echo - Deteccao automatica de configuracao de audio
echo - Filtros adaptativos baseados no numero de canais
echo - Validacao completa de integridade dos arquivos
echo - Logs detalhados para diagnostico
echo - Testes de reproduzibilidade
echo - Tratamento robusto de erros
echo.
pause
exit /b 0

:erro
echo.
echo [ERRO] Teste falhou. Verifique as ferramentas necessarias.
pause
exit /b 1
