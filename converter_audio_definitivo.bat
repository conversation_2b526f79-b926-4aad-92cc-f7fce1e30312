@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONVERSOR DE AUDIO DEFINITIVO PARA KODI
echo   Configurado com caminhos corretos detectados
echo ===================================================
echo.

REM Caminhos detectados automaticamente
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"
set "temp_folder=%audio_folder%\temp"
set "log_folder=%base_folder%logs"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mkvextract_exe=C:\MKVToolNix\mkvextract.exe"

REM Criar estrutura de pastas
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"
if not exist "%temp_folder%" mkdir "%temp_folder%"
if not exist "%log_folder%" mkdir "%log_folder%"

echo Estrutura de pastas criada.
echo.

REM Verificar ferramentas
echo Verificando ferramentas...

ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] FFmpeg nao encontrado.
    pause
    exit /b 1
)
echo [OK] FFmpeg encontrado.

if not exist "!mkvextract_exe!" (
    echo [ERRO] MKVToolNix nao encontrado em: !mkvextract_exe!
    pause
    exit /b 1
)
echo [OK] MKVToolNix encontrado.

if not exist "!mediainfo_cli!" (
    echo [ERRO] MediaInfo CLI nao encontrado em: !mediainfo_cli!
    pause
    exit /b 1
)
echo [OK] MediaInfo CLI encontrado.

echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
pause

REM Listar arquivos MKV
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. "%%~nxF"
)

if "!i!"=="0" (
    echo Nenhum arquivo .mkv encontrado.
    pause
    exit /b 1
)

REM Selecionar arquivo
:selecionar_arquivo
set /p "sel=Escolha o numero do arquivo: "
if "!sel!"=="" goto :selecionar_arquivo

set "valid=0"
for /l %%n in (1,1,!i!) do (
    if "!sel!"=="%%n" set "valid=1"
)

if "!valid!"=="0" (
    echo ERRO: Opcao invalida.
    goto :selecionar_arquivo
)

set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

REM Obter nome base
set /p "movie_base=Digite o nome base do filme (sem espacos): "
if "!movie_base!"=="" (
    echo ERRO: Nome base e obrigatorio.
    pause
    exit /b 1
)

REM Analisar faixas de audio
echo.
echo ===================================================
echo ANALISANDO: !selected_filename!
echo ===================================================

echo Detectando faixas de audio...
set "audio_list=%temp_folder%\audio_list.txt"
"!mediainfo_cli!" --Inform="Audio;%%ID%%|%%Language%%|%%Format%%|%%Channels%%|%%SamplingRate%%\n" "!selected_file!" > "!audio_list!" 2>nul

if not exist "!audio_list!" (
    echo ERRO: Nao foi possivel detectar faixas de audio.
    pause
    exit /b 1
)

echo.
echo Faixas de audio disponiveis:
echo ID | Idioma | Formato | Canais | Taxa
echo ---|--------|---------|--------|------

set "track_count=0"
set "selected_track_id="

for /f "tokens=1,2,3,4,5 delims=|" %%a in (!audio_list!) do (
    if not "%%a"=="" (
        set /a track_count+=1
        echo %%a  ^| %%b     ^| %%c     ^| %%d    ^| %%e
        
        REM Auto-selecionar faixa em portugues se encontrada
        if /i "%%b"=="pt" set "selected_track_id=%%a"
        if /i "%%b"=="por" set "selected_track_id=%%a"
        if /i "%%b"=="portuguese" set "selected_track_id=%%a"
        
        REM Se nao encontrou portugues, usar primeira faixa
        if "!selected_track_id!"=="" set "selected_track_id=%%a"
    )
)

del "!audio_list!"

if "!selected_track_id!"=="" (
    echo ERRO: Nenhuma faixa de audio encontrada.
    pause
    exit /b 1
)

echo.
echo Faixa selecionada automaticamente: ID !selected_track_id!
set /p "confirm=Confirmar esta faixa? (s/n): "

if /i not "!confirm!"=="s" (
    set /p "selected_track_id=Digite o ID da faixa desejada: "
)

REM Extrair audio
echo.
echo ===================================================
echo EXTRAINDO AUDIO
echo ===================================================

set "audio_output=%temp_folder%\!movie_base!_original.mka"
set "log_file=%log_folder%\extracao_!movie_base!.log"

echo Extraindo faixa ID !selected_track_id!...
"!mkvextract_exe!" tracks "!selected_file!" !selected_track_id!:"!audio_output!" > "!log_file!" 2>&1

if not exist "!audio_output!" (
    echo ERRO: Falha na extracao de audio.
    echo Verifique o log: !log_file!
    pause
    exit /b 1
)

echo Audio extraido com sucesso!

REM Validar audio extraido
echo Validando audio extraido...
for %%A in ("!audio_output!") do set "file_size=%%~zA"
if !file_size! lss 1024 (
    echo ERRO: Arquivo de audio muito pequeno.
    pause
    exit /b 1
)

ffmpeg -v error -i "!audio_output!" -f null - >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Arquivo de audio corrompido.
    pause
    exit /b 1
)

echo [OK] Audio extraido e valido.

REM Detectar configuracao do audio
echo Detectando configuracao do audio...
set "audio_info=%temp_folder%\audio_info.txt"
ffprobe -v quiet -print_format json -show_streams "!audio_output!" > "!audio_info!" 2>nul

set "channels=2"
set "sample_rate=48000"

for /f "tokens=2 delims=:" %%a in ('findstr "channels" "!audio_info!" 2^>nul') do (
    set "channels_raw=%%a"
    set "channels=!channels_raw: =!"
    set "channels=!channels:,=!"
)

for /f "tokens=2 delims=:" %%a in ('findstr "sample_rate" "!audio_info!" 2^>nul') do (
    set "sample_rate_raw=%%a"
    set "sample_rate=!sample_rate_raw: =!"
    set "sample_rate=!sample_rate:,=!"
    set "sample_rate=!sample_rate:"=!"
)

if exist "!audio_info!" del "!audio_info!"

echo Configuracao detectada: !channels! canais, !sample_rate! Hz

REM Definir filtros baseados na configuracao
if !channels! geq 6 (
    set "filter_dolby=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL+0.1*LFE|FR=0.325*FR+0.230*FC+0.325*BR+0.1*LFE"
    set "filter_voz=pan=stereo|FL=0.25*FL+0.5*FC+0.15*BL+0.1*LFE|FR=0.25*FR+0.5*FC+0.15*BR+0.1*LFE"
) else if !channels! equ 4 (
    set "filter_dolby=pan=stereo|FL=0.4*FL+0.3*BL|FR=0.4*FR+0.3*BR"
    set "filter_voz=pan=stereo|FL=0.6*FL+0.4*BL|FR=0.6*FR+0.4*BR"
) else (
    set "filter_dolby=volume=0.9,dynaudnorm=f=75:g=25:p=0.95"
    set "filter_voz=volume=1.1,dynaudnorm=f=150:g=31:p=0.95,highpass=f=100,lowpass=f=8000"
)

REM Selecionar modo de conversao
echo.
echo ===================================================
echo OPCOES DE CONVERSAO
echo ===================================================
echo.
echo 1. Dolby Pro Logic II (otimizado para sistemas surround)
echo 2. Voz mais clara (enfase em dialogos)
echo 3. Ambas as versoes
echo 4. Apenas converter para FLAC (sem filtros)
echo.

:loop_modo
set /p "convert_mode=Escolha uma opcao (1-4): "

if "!convert_mode!"=="1" goto :modo_valido
if "!convert_mode!"=="2" goto :modo_valido
if "!convert_mode!"=="3" goto :modo_valido
if "!convert_mode!"=="4" goto :modo_valido

echo ERRO: Opcao invalida.
goto :loop_modo

:modo_valido

echo.
echo ===================================================
echo CONVERTENDO AUDIO
echo ===================================================

REM Converter baseado na escolha
if "!convert_mode!"=="1" call :converter_dolby
if "!convert_mode!"=="2" call :converter_voz
if "!convert_mode!"=="3" (
    call :converter_dolby
    call :converter_voz
)
if "!convert_mode!"=="4" call :converter_simples

REM Validacao final
echo.
echo ===================================================
echo VALIDACAO FINAL
echo ===================================================

set "arquivos_ok=0"
set "total_arquivos=0"

for %%F in ("!convertido_folder!\!movie_base!*.flac") do (
    set /a total_arquivos+=1
    echo Testando: %%~nxF
    
    ffmpeg -i "%%F" -t 5 -f null - >nul 2>&1
    if !errorlevel! equ 0 (
        set /a arquivos_ok+=1
        echo [OK] %%~nxF funcionando.
    ) else (
        echo [ERRO] %%~nxF com problemas.
    )
)

echo.
echo Arquivos validados: !arquivos_ok! de !total_arquivos!

REM Limpeza
echo.
set /p "cleanup=Remover arquivos temporarios? (s/n): "
if /i "!cleanup!"=="s" (
    if exist "%temp_folder%" rmdir /s /q "%temp_folder%"
    echo Arquivos temporarios removidos.
)

echo.
echo ===================================================
echo CONVERSAO CONCLUIDA COM SUCESSO!
echo Arquivos estao em: "!convertido_folder!"
echo Nomes prontos para o Kodi reconhecer.
echo ===================================================
pause
exit /b 0

REM ===================================================
REM FUNCOES DE CONVERSAO
REM ===================================================

:converter_dolby
echo Convertendo para Dolby Pro Logic II...
set "output_dolby=!convertido_folder!\!movie_base!-pt-BR_dolby.flac"
set "log_dolby=%log_folder%\conversao_dolby_!movie_base!.log"

ffmpeg -i "!audio_output!" -af "!filter_dolby!" -c:a flac -compression_level 8 -sample_fmt s16 -ar !sample_rate! "!output_dolby!" -y > "!log_dolby!" 2>&1

if %errorlevel% neq 0 (
    echo ERRO: Falha na conversao Dolby Pro Logic II.
    echo Verifique o log: !log_dolby!
    pause
    exit /b 1
)

echo [OK] Conversao Dolby Pro Logic II concluida.
goto :eof

:converter_voz
echo Convertendo para Voz mais clara...
set "output_voz=!convertido_folder!\!movie_base!-pt-BR_voz.flac"
set "log_voz=%log_folder%\conversao_voz_!movie_base!.log"

ffmpeg -i "!audio_output!" -af "!filter_voz!" -c:a flac -compression_level 8 -sample_fmt s16 -ar !sample_rate! "!output_voz!" -y > "!log_voz!" 2>&1

if %errorlevel% neq 0 (
    echo ERRO: Falha na conversao Voz clara.
    echo Verifique o log: !log_voz!
    pause
    exit /b 1
)

echo [OK] Conversao Voz clara concluida.
goto :eof

:converter_simples
echo Convertendo para FLAC sem filtros...
set "output_simples=!convertido_folder!\!movie_base!-pt-BR_original.flac"
set "log_simples=%log_folder%\conversao_simples_!movie_base!.log"

ffmpeg -i "!audio_output!" -c:a flac -compression_level 8 -sample_fmt s16 -ar !sample_rate! "!output_simples!" -y > "!log_simples!" 2>&1

if %errorlevel% neq 0 (
    echo ERRO: Falha na conversao simples.
    echo Verifique o log: !log_simples!
    pause
    exit /b 1
)

echo [OK] Conversao simples concluida.
goto :eof
