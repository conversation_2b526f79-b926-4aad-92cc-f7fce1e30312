@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   Extracao e Conversao de Audio para o Kodi
echo   Com suporte a Dolby Pro Logic II e Voz clara
echo ===================================================
echo.
echo IMPORTANTE: Certifique-se de ter instalado:
echo - FFmpeg
echo - MediaInfo CLI (detectado em C:\MediaInfo_CLI_25.04_Windows_x64\)
echo - MKVToolNix
echo.
echo Pressione qualquer tecla para continuar...
pause >nul
echo.

REM Pasta dos arquivos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"

if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"

echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
echo.
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. %%~nxF
)

if "!i!"=="0" (
    echo Nenhum arquivo encontrado.
    echo.
    pause
    exit /b
)

set /p "sel=Escolha o numero do arquivo: "

REM Validar entrada do usuario
if "!sel!"=="" (
    echo ERRO: Nenhuma opcao selecionada.
    pause
    exit /b
)

REM Verificar se a selecao e um numero valido
set "valid=0"
for /l %%n in (1,1,!i!) do (
    if "!sel!"=="%%n" set "valid=1"
)

if "!valid!"=="0" (
    echo ERRO: Opcao invalida. Escolha um numero entre 1 e !i!.
    pause
    exit /b
)

set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

REM Nome base para os arquivos convertidos
set /p "movie_base=Digite o nome base do filme (ex: filme_suspense_2024): "

echo.
echo Detectando faixas de audio em: !selected_filename!
echo.

REM Tentar extrair informacoes de audio
echo Analisando faixas de audio...
set "audio_track_index="
set "track_count=0"

REM Definir caminho do MediaInfo CLI
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"

REM Verificar se MediaInfo CLI existe
if not exist "!mediainfo_cli!" (
    echo AVISO: MediaInfo CLI nao encontrado em: !mediainfo_cli!
    echo Tentando usar MediaInfo do PATH...
    set "mediainfo_cli=MediaInfo"
)

REM Usar MediaInfo para listar faixas
echo.
echo Executando: MediaInfo para detectar faixas...
"!mediainfo_cli!" --Inform="Audio;%%StreamOrder%%,%%Language%%\n" "!selected_file!" > "%base_folder%audio_tracks.txt" 2>nul

if exist "%base_folder%audio_tracks.txt" (
    echo Faixas de audio encontradas:
    set "line_num=0"
    for /f "tokens=1,2 delims=," %%a in (%base_folder%audio_tracks.txt) do (
        set /a line_num+=1
        echo Faixa !line_num!: Idioma %%b (ID: %%a)
        if /i "%%b"=="pt" set "audio_track_index=%%a"
        if /i "%%b"=="por" set "audio_track_index=%%a"
        if /i "%%b"=="portuguese" set "audio_track_index=%%a"
        if "%%b"=="" if "!audio_track_index!"=="" set "audio_track_index=%%a"
    )
    del "%base_folder%audio_tracks.txt"
) else (
    echo AVISO: Nao foi possivel detectar automaticamente.
    echo Tentando usar a primeira faixa de audio...
    set "audio_track_index=0"
)

if not defined audio_track_index (
    echo.
    echo ERRO: Nenhuma faixa de audio detectada.
    echo Verifique se o arquivo e valido.
    pause
    exit /b
)

echo.
echo Usando faixa de audio no indice: !audio_track_index!

REM Extrair audio
set "audio_output=!audio_folder!\!movie_base!_orig.mka"
echo.
echo Extraindo faixa de audio...
echo Executando: mkvextract tracks "!selected_file!" !audio_track_index!:"!audio_output!"

mkvextract tracks "!selected_file!" !audio_track_index!:"!audio_output!" 2>nul

if not exist "!audio_output!" (
    echo ERRO: Falha na extracao de audio.
    echo Verifique se o MKVToolNix esta instalado e funcionando.
    echo.
    pause
    exit /b
)

echo Audio extraido com sucesso: !audio_output!

echo.
echo Deseja converter para:
echo 1. Apenas Dolby Pro Logic II
echo 2. Apenas Voz mais clara
echo 3. Ambas as versoes
set /p "convert_mode=Opcao (1-3): "

REM Validar modo de conversao
if not "!convert_mode!"=="1" if not "!convert_mode!"=="2" if not "!convert_mode!"=="3" (
    echo ERRO: Opcao invalida. Escolha 1, 2 ou 3.
    pause
    exit /b
)

REM Definir os filtros
set "filter_dolby=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
set "filter_voz=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"

echo.
echo Iniciando conversao...

REM Converter de acordo com a escolha
if "!convert_mode!"=="1" (
    echo Convertendo para Dolby Pro Logic II...
    ffmpeg -i "!audio_output!" -af "!filter_dolby!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" -y 2>nul
    if exist "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" (
        echo Conversao Dolby Pro Logic II concluida.
    ) else (
        echo ERRO: Falha na conversao Dolby Pro Logic II.
        pause
        exit /b
    )
)

if "!convert_mode!"=="2" (
    echo Convertendo para Voz mais clara...
    ffmpeg -i "!audio_output!" -af "!filter_voz!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_voz.flac" -y 2>nul
    if exist "!convertido_folder!\!movie_base!-pt-BR_voz.flac" (
        echo Conversao Voz clara concluida.
    ) else (
        echo ERRO: Falha na conversao Voz clara.
        pause
        exit /b
    )
)

if "!convert_mode!"=="3" (
    echo Convertendo para Dolby Pro Logic II...
    ffmpeg -i "!audio_output!" -af "!filter_dolby!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" -y 2>nul
    if exist "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" (
        echo Conversao Dolby Pro Logic II concluida.
    ) else (
        echo ERRO: Falha na conversao Dolby Pro Logic II.
        pause
        exit /b
    )

    echo Convertendo para Voz mais clara...
    ffmpeg -i "!audio_output!" -af "!filter_voz!" -c:a flac -compression_level 5 "!convertido_folder!\!movie_base!-pt-BR_voz.flac" -y 2>nul
    if exist "!convertido_folder!\!movie_base!-pt-BR_voz.flac" (
        echo Conversao Voz clara concluida.
    ) else (
        echo ERRO: Falha na conversao Voz clara.
        pause
        exit /b
    )
)

REM Limpeza do arquivo temporario
if exist "!audio_output!" (
    echo.
    set /p "cleanup=Deseja remover o arquivo de audio temporario? (s/n): "
    if /i "!cleanup!"=="s" (
        del "!audio_output!"
        echo Arquivo temporario removido.
    )
)

echo.
echo ===================================================
echo Conversao concluida com sucesso!
echo Arquivos estao em: "!convertido_folder!"
echo Nomes prontos para o Kodi reconhecer.
echo.
echo DICA: Para usar no Kodi, coloque os arquivos .flac
echo na mesma pasta do filme com o mesmo nome base.
echo ===================================================
echo.
pause
