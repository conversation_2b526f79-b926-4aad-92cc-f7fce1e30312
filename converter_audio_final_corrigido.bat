@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONVERSOR DE AUDIO FINAL PARA KODI
echo   Versao corrigida - Aceita espacos e nao fecha
echo ===================================================
echo.

REM Caminhos das ferramentas
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"
set "temp_folder=%audio_folder%\temp"
set "log_folder=%base_folder%logs"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mkvextract_exe=C:\MKVToolNix\mkvextract.exe"

REM Criar pastas
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"
if not exist "%temp_folder%" mkdir "%temp_folder%"
if not exist "%log_folder%" mkdir "%log_folder%"

echo Estrutura de pastas criada.
echo.

REM Verificar ferramentas
echo Verificando ferramentas...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] FFmpeg nao encontrado.
    pause
    exit /b 1
)
echo [OK] FFmpeg encontrado.

if not exist "!mkvextract_exe!" (
    echo [ERRO] MKVToolNix nao encontrado.
    pause
    exit /b 1
)
echo [OK] MKVToolNix encontrado.

if not exist "!mediainfo_cli!" (
    echo [ERRO] MediaInfo CLI nao encontrado.
    pause
    exit /b 1
)
echo [OK] MediaInfo CLI encontrado.

echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. "%%~nxF"
)

if "!i!"=="0" (
    echo Nenhum arquivo .mkv encontrado.
    pause
    exit /b 1
)

REM Selecionar arquivo com validacao
:selecionar_arquivo
set /p "sel=Escolha o numero do arquivo: "

if "!sel!"=="" (
    echo.
    echo ERRO: Digite um numero.
    echo Pressione qualquer tecla para tentar novamente...
    pause >nul
    goto :selecionar_arquivo
)

set "valid=0"
for /l %%n in (1,1,!i!) do (
    if "!sel!"=="%%n" set "valid=1"
)

if "!valid!"=="0" (
    echo.
    echo ERRO: Numero invalido. Escolha entre 1 e !i!.
    echo Pressione qualquer tecla para tentar novamente...
    pause >nul
    goto :selecionar_arquivo
)

set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

REM Obter nome do filme com tratamento de espacos
echo.
echo Digite o nome do filme (pode conter espacos):
echo Exemplo: Avatar O Caminho da Agua 2023
echo.

:obter_nome
set /p "movie_name=Nome do filme: "

if "!movie_name!"=="" (
    echo.
    echo ERRO: Digite um nome para o filme.
    echo Pressione qualquer tecla para tentar novamente...
    pause >nul
    goto :obter_nome
)

REM Converter nome para formato de arquivo (simples e eficaz)
set "movie_base=!movie_name!"

REM Substituir caracteres problematicos um por vez
call :limpar_nome

echo.
echo Nome do arquivo sera: !movie_base!

:confirmar_nome
set /p "confirmar=Confirmar este nome? (s/n): "

if "!confirmar!"=="" (
    echo ERRO: Digite s ou n.
    goto :confirmar_nome
)

if /i "!confirmar!"=="n" goto :obter_nome
if /i "!confirmar!"=="nao" goto :obter_nome
if /i not "!confirmar!"=="s" if /i not "!confirmar!"=="sim" (
    echo ERRO: Digite s para sim ou n para nao.
    goto :confirmar_nome
)

REM Analisar faixas de audio
echo.
echo ===================================================
echo ANALISANDO: !selected_filename!
echo ===================================================

echo Detectando faixas de audio...
set "audio_list=%temp_folder%\audio_list.txt"
"!mediainfo_cli!" --Inform="Audio;%%ID%%|%%Language%%|%%Format%%|%%Channels%%\n" "!selected_file!" > "!audio_list!" 2>nul

if not exist "!audio_list!" (
    echo ERRO: Nao foi possivel analisar o arquivo.
    pause
    exit /b 1
)

echo.
echo Faixas de audio disponiveis:
echo ID | Idioma | Formato | Canais
echo ---|--------|---------|--------

set "track_count=0"
set "selected_track_id="

for /f "tokens=1,2,3,4 delims=|" %%a in (!audio_list!) do (
    if not "%%a"=="" (
        set /a track_count+=1
        echo %%a  ^| %%b     ^| %%c     ^| %%d
        
        if /i "%%b"=="pt" set "selected_track_id=%%a"
        if /i "%%b"=="por" set "selected_track_id=%%a"
        if /i "%%b"=="portuguese" set "selected_track_id=%%a"
        
        if "!selected_track_id!"=="" set "selected_track_id=%%a"
    )
)

del "!audio_list!"

if "!selected_track_id!"=="" (
    echo ERRO: Nenhuma faixa de audio encontrada.
    pause
    exit /b 1
)

echo.
echo Faixa selecionada: ID !selected_track_id!

:confirmar_faixa
set /p "confirm_track=Confirmar esta faixa? (s/n): "

if "!confirm_track!"=="" (
    echo ERRO: Digite s ou n.
    goto :confirmar_faixa
)

if /i "!confirm_track!"=="n" (
    set /p "selected_track_id=Digite o ID da faixa desejada: "
)

if /i not "!confirm_track!"=="s" if /i not "!confirm_track!"=="sim" if /i not "!confirm_track!"=="n" if /i not "!confirm_track!"=="nao" (
    echo ERRO: Digite s para sim ou n para nao.
    goto :confirmar_faixa
)

REM Extrair audio
echo.
echo ===================================================
echo EXTRAINDO AUDIO
echo ===================================================

set "audio_output=%temp_folder%\!movie_base!_original.mka"
set "log_file=%log_folder%\extracao_!movie_base!.log"

echo Extraindo faixa ID !selected_track_id!...
"!mkvextract_exe!" tracks "!selected_file!" !selected_track_id!:"!audio_output!" > "!log_file!" 2>&1

if not exist "!audio_output!" (
    echo ERRO: Falha na extracao de audio.
    echo Verifique o log: !log_file!
    pause
    exit /b 1
)

echo Audio extraido com sucesso!

REM Validar audio
echo Validando audio extraido...
for %%A in ("!audio_output!") do set "file_size=%%~zA"
if !file_size! lss 1024 (
    echo ERRO: Arquivo de audio muito pequeno.
    pause
    exit /b 1
)

ffmpeg -v error -i "!audio_output!" -f null - >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Arquivo de audio corrompido.
    pause
    exit /b 1
)

echo [OK] Audio valido.

REM Selecionar modo de conversao
echo.
echo ===================================================
echo OPCOES DE CONVERSAO
echo ===================================================
echo.
echo 1. Dolby Pro Logic II
echo 2. Voz mais clara
echo 3. Ambas as versoes
echo 4. Apenas FLAC (sem filtros)
echo.

:loop_modo
set /p "convert_mode=Escolha uma opcao (1-4): "

if "!convert_mode!"=="" (
    echo.
    echo ERRO: Digite um numero de 1 a 4.
    echo Pressione qualquer tecla para tentar novamente...
    pause >nul
    goto :loop_modo
)

if "!convert_mode!"=="1" goto :modo_ok
if "!convert_mode!"=="2" goto :modo_ok
if "!convert_mode!"=="3" goto :modo_ok
if "!convert_mode!"=="4" goto :modo_ok

echo.
echo ERRO: Opcao invalida. Digite 1, 2, 3 ou 4.
echo Pressione qualquer tecla para tentar novamente...
pause >nul
goto :loop_modo

:modo_ok

echo.
echo ===================================================
echo CONVERTENDO AUDIO
echo ===================================================

REM Converter baseado na escolha
if "!convert_mode!"=="1" call :converter_dolby
if "!convert_mode!"=="2" call :converter_voz
if "!convert_mode!"=="3" (
    call :converter_dolby
    call :converter_voz
)
if "!convert_mode!"=="4" call :converter_simples

echo.
echo ===================================================
echo CONVERSAO CONCLUIDA!
echo Arquivos estao em: "!convertido_folder!"
echo ===================================================

REM Limpeza
echo.
set /p "cleanup=Remover arquivos temporarios? (s/n): "
if /i "!cleanup!"=="s" (
    if exist "%temp_folder%" rmdir /s /q "%temp_folder%"
    echo Arquivos temporarios removidos.
)

pause
exit /b 0

REM ===================================================
REM FUNCOES
REM ===================================================

:limpar_nome
REM Funcao para limpar caracteres problematicos
set "movie_base=!movie_base: =_!"
set "movie_base=!movie_base::=_!"
set "movie_base=!movie_base:?=!"
set "movie_base=!movie_base:*=!"
set "movie_base=!movie_base:<=!"
set "movie_base=!movie_base:>=!"
set "movie_base=!movie_base:|=!"
set "movie_base=!movie_base:/=!"
set "movie_base=!movie_base:\=!"
set "movie_base=!movie_base:"=!"
set "movie_base=!movie_base:(=!"
set "movie_base=!movie_base:)=!"
set "movie_base=!movie_base:[=!"
set "movie_base=!movie_base:]=!"
goto :eof

:converter_dolby
echo Convertendo para Dolby Pro Logic II...
set "output_dolby=!convertido_folder!\!movie_base!-pt-BR_dolby.flac"
ffmpeg -i "!audio_output!" -af "pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR" -c:a flac -compression_level 8 "!output_dolby!" -y >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Dolby Pro Logic II concluido.
) else (
    echo [ERRO] Falha na conversao Dolby.
)
goto :eof

:converter_voz
echo Convertendo para Voz mais clara...
set "output_voz=!convertido_folder!\!movie_base!-pt-BR_voz.flac"
ffmpeg -i "!audio_output!" -af "pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR" -c:a flac -compression_level 8 "!output_voz!" -y >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Voz clara concluida.
) else (
    echo [ERRO] Falha na conversao Voz.
)
goto :eof

:converter_simples
echo Convertendo para FLAC sem filtros...
set "output_simples=!convertido_folder!\!movie_base!-pt-BR_original.flac"
ffmpeg -i "!audio_output!" -c:a flac -compression_level 8 "!output_simples!" -y >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Conversao simples concluida.
) else (
    echo [ERRO] Falha na conversao simples.
)
goto :eof
