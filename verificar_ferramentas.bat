@echo off
echo ===================================================
echo   VERIFICACAO DE FERRAMENTAS NECESSARIAS
echo ===================================================
echo.

echo Testando FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] FFmpeg esta instalado e funcionando!
    ffmpeg -version 2>&1 | findstr "ffmpeg version"
) else (
    echo [ERRO] FFmpeg NAO encontrado!
)
echo.

echo Testando MediaInfo...
MediaInfo --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] MediaInfo esta instalado e funcionando!
    MediaInfo --version 2>&1
) else (
    echo [ERRO] MediaInfo NAO encontrado!
)
echo.

echo Testando MKVToolNix (mkvextract)...
mkvextract --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] MKVToolNix esta instalado e funcionando!
    mkvextract --version 2>&1 | findstr "mkvextract"
) else (
    echo [ERRO] MKVToolNix NAO encontrado!
)
echo.

echo ===================================================
echo   RESUMO DA VERIFICACAO
echo ===================================================

set "tools_ok=0"

ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 set /a tools_ok+=1

MediaInfo --version >nul 2>&1
if %errorlevel% equ 0 set /a tools_ok+=1

mkvextract --version >nul 2>&1
if %errorlevel% equ 0 set /a tools_ok+=1

echo Ferramentas funcionando: %tools_ok% de 3

if %tools_ok% equ 3 (
    echo.
    echo [SUCESSO] Todas as ferramentas estao instaladas!
    echo O script converter_audio_final.bat deve funcionar perfeitamente.
) else (
    echo.
    echo [ATENCAO] Algumas ferramentas estao faltando.
    echo Instale as ferramentas em falta antes de usar o script.
)

echo.
pause
