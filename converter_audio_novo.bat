@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   Extração e Conversão de Áudio para o Kodi
echo   Com suporte a Dolby Pro Logic II e Voz clara
echo ===================================================
echo.

REM Verificar ferramentas necessárias
where ffmpeg >nul 2>&1 || (echo FFmpeg nao encontrado. && pause && exit /b)
where MediaInfo >nul 2>&1 || (echo MediaInfo nao encontrado. && pause && exit /b)
where mkvextract >nul 2>&1 || (echo MKVToolNix (mkvextract) nao encontrado. && pause && exit /b)

REM Pasta dos arquivos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"

if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"

echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. %%~nxF
)

if "!i!"=="0" (
    echo Nenhum arquivo encontrado.
    pause
    exit /b
)

set /p "sel=Escolha o numero do arquivo: "
set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

REM Nome base para os arquivos convertidos
set /p "movie_base=Digite o nome base do filme (ex: filme_suspense_2024): "

echo.
echo Detectando faixas de audio em: !selected_filename!
MediaInfo --Output=JSON "!selected_file!" > "%base_folder%mediainfo.json"

REM Extrair índice da faixa de audio em português
for /f "tokens=1 delims=:" %%a in ('MediaInfo --Inform^="Audio;%Language%,%ID%\n" "!selected_file!" ^| findstr "pt"') do (
    set "audio_track_index=%%a"
    goto :found
)

:found
if not defined audio_track_index (
    echo ERRO: Nenhuma faixa em português encontrada.
    pause
    exit /b
)

echo.
echo Faixa em portugues encontrada no indice: %audio_track_index%

REM Extrair audio
set "audio_output=%audio_folder%\%movie_base%_orig.mka"
mkvextract tracks "!selected_file!" %audio_track_index%:"%audio_output%"

if not exist "%audio_output%" (
    echo ERRO: Falha na extracao de audio.
    pause
    exit /b
)

echo.
echo Deseja converter para:
echo 1. Apenas Dolby Pro Logic II
echo 2. Apenas Voz mais clara
echo 3. Ambas as versoes
set /p "convert_mode=Opcao (1-3): "

REM Definir os filtros
set "filter_dolby=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
set "filter_voz=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"

REM Converter de acordo com a escolha
if "%convert_mode%"=="1" (
    ffmpeg -i "%audio_output%" -af "%filter_dolby%" -c:a flac "%convertido_folder%\%movie_base%-pt-BR_dolby.flac"
)

if "%convert_mode%"=="2" (
    ffmpeg -i "%audio_output%" -af "%filter_voz%" -c:a flac "%convertido_folder%\%movie_base%-pt-BR_voz.flac"
)

if "%convert_mode%"=="3" (
    ffmpeg -i "%audio_output%" -af "%filter_dolby%" -c:a flac "%convertido_folder%\%movie_base%-pt-BR_dolby.flac"
    ffmpeg -i "%audio_output%" -af "%filter_voz%" -c:a flac "%convertido_folder%\%movie_base%-pt-BR_voz.flac"
)

echo.
echo ===================================================
echo Conversao concluida com sucesso!
echo Arquivos estao em: "%convertido_folder%"
echo Nomes prontos para o Kodi reconhecer.
echo ===================================================
pause
exit /b
