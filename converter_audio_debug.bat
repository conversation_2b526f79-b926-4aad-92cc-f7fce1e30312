@echo off
setlocal enabledelayedexpansion

echo ================================================
echo   CONVERSOR DE AUDIO 5.1 PARA 2.0 ESTÉREO
echo   COM EXTRAÇÃO AUTOMÁTICA DE MKV E NOMES KODI
echo ================================================
echo.

REM Caminhos dos utilitários
set "mediainfo=MediaInfo.exe"
set "mkvextract=mkvextract.exe"
set "ffmpeg=ffmpeg.exe"

REM Testar comandos básicos
echo Testando ferramentas essenciais...
where %mediainfo% >nul 2>&1 || (echo ERRO: MediaInfo não encontrado & pause & exit /b)
where %mkvextract% >nul 2>&1 || (echo ERRO: MKVToolNix (mkvextract) não encontrado & pause & exit /b)
where %ffmpeg% >nul 2>&1 || (echo ERRO: FFmpeg não encontrado & pause & exit /b)

echo Ferramentas OK.
echo.

REM Criar pastas
set "base_dir=%~dp0"
set "filmes_dir=%base_dir%filmes"
set "output_dir=%base_dir%convertido"

if not exist "%filmes_dir%" (
    mkdir "%filmes_dir%"
    echo A pasta "filmes" foi criada. Coloque os arquivos MKV nela e reinicie o script.
    pause
    exit /b
)

if not exist "%output_dir%" (
    mkdir "%output_dir%"
)

echo Procurando arquivos MKV na pasta "filmes"...
set /a count=0
for %%F in ("%filmes_dir%\*.mkv") do (
    set /a count+=1
    set "file_!count!=%%F"
    echo !count!. %%~nxF
)

if !count! == 0 (
    echo Nenhum arquivo .mkv encontrado na pasta "filmes".
    pause
    exit /b
)

echo.
set /p "sel=Digite o numero do arquivo desejado (1 a !count!): "

set "selected_file=!file_%sel%!"
if not exist "!selected_file!" (
    echo Arquivo invalido.
    pause
    exit /b
)

set "basename="
for %%F in ("!selected_file!") do set "basename=%%~nF"

echo Arquivo selecionado: !basename!
pause

REM Verificar trilhas de áudio
echo Verificando trilhas de áudio com MediaInfo...
%mediainfo% --Inform=Audio;%ID%:"%Language%":"%Format%"\\n "!selected_file!" > "%base_dir%audios.txt"

echo Trilhas de áudio encontradas:
type "%base_dir%audios.txt"
pause

REM Escolher ID da faixa dublada para extrair
set /p "audio_id=Digite o ID da faixa dublada (ex: 1): "

REM Extrair faixa de áudio
set "audio_raw=%base_dir%temp_audio.dts"
echo Extraindo áudio dublado...
%mkvextract% tracks "!selected_file!" %audio_id%:"!audio_raw!" || (echo ERRO ao extrair faixa & pause & exit /b)

REM Pedir nome base do filme (Kodi)
set /p "kodi_name=Digite o nome do arquivo principal do filme (exato, sem extensão): "

REM Modos de conversão
echo.
echo Selecione os modos de conversão desejados:
echo 1 - Dolby Pro Logic II (original)
echo 2 - Modo voz clara
echo 3 - Modo simples (só canais frontais)
echo (Você pode escolher mais de um separando com vírgula, ex: 1,2)
set /p "modes=Digite os modos: "

for %%M in (%modes%) do (
    set "pan="
    if "%%M"=="1" set "pan=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
    if "%%M"=="2" set "pan=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"
    if "%%M"=="3" set "pan=pan=stereo|FL=1.0*FL|FR=1.0*FR"

    if not defined pan (
        echo Modo "%%M" invalido, pulando...
    ) else (
        if "%%M"=="1" set "outname=%output_dir%\%kodi_name%.flac"
        if "%%M"=="2" set "outname=%output_dir%\%kodi_name%.voice.flac"
        if "%%M"=="3" set "outname=%output_dir%\%kodi_name%.simple.flac"

        echo.
        echo Convertendo usando modo %%M...
        %ffmpeg% -i "!audio_raw!" -af "!pan!" -c:a flac "!outname!" || (echo ERRO durante a conversão & pause & exit /b)
        echo Arquivo gerado: !outname!
    )
)

echo.
echo Limpando arquivos temporários...
del /q "!audio_raw!"

echo.
echo ================================================
echo CONVERSÃO CONCLUÍDA COM SUCESSO
echo Os arquivos foram salvos na pasta "convertido"
echo ================================================
pause
exit /b
