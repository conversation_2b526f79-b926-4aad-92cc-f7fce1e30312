@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   Conversor de Audio 5.1 para 2.0 (Estereo)
echo   Qualidade Maxima Sem Perdas (.flac)
echo ===================================================
echo.

REM Verificar se o FFmpeg esta instalado
where ffmpeg >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: FFmpeg nao encontrado. Verifique se esta instalado e no PATH.
    echo.
    echo Pressione qualquer tecla para sair...
    pause >nul
    exit /b 1
)

REM Definir caminhos
set "audio_folder=%~dp0audio"
set "output_folder=%~dp0audio\convertido"

if not exist "%audio_folder%" (
    mkdir "%audio_folder%"
)

if not exist "%output_folder%" (
    mkdir "%output_folder%"
)

echo Arquivos de audio encontrados:
set "file_count=0"
for %%F in ("%audio_folder%\*.mka" "%audio_folder%\*.ac3" "%audio_folder%\*.dts" "%audio_folder%\*.eac3" "%audio_folder%\*.aac" "%audio_folder%\*.mp3" "%audio_folder%\*.flac" "%audio_folder%\*.wav") do (
    if exist "%%F" (
        set /a file_count+=1
        set "file_!file_count!=%%F"
        set "filename_!file_count!=%%~nxF"
        echo !file_count!. %%~nxF
    )
)

if %file_count%==0 (
    echo Nenhum arquivo encontrado em "%audio_folder%"
    pause >nul
    exit /b
)

set /p "selected_file=Selecione o numero do arquivo (1-%file_count%): "

REM Validar seleção
if !selected_file! LSS 1 if !selected_file! GTR %file_count% (
    echo Selecao invalida.
    pause >nul
    exit /b
)

set "audio_file=!file_%selected_file%!"
set "audio_filename=!filename_%selected_file%!"

echo.
echo Escolha o modo de conversao:
echo 1. Modo Dolby Pro Logic II (fiel ao som original)
echo 2. Modo voz mais clara
echo 3. Modo simples (somente canais frontais)
set /p "mode=Digite o numero do modo desejado (1-3): "

REM Definir filtro pan com base no modo
if "%mode%"=="1" (
    set "pan_filter=pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR"
)
if "%mode%"=="2" (
    set "pan_filter=pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR"
)
if "%mode%"=="3" (
    set "pan_filter=pan=stereo|FL=1.0*FL|FR=1.0*FR"
)

if not defined pan_filter (
    echo Modo invalido. Abortando...
    pause >nul
    exit /b
)

echo.
echo Arquivo selecionado: !audio_filename!
for %%F in ("!audio_file!") do set "output_basename=%%~nF"
set "output_file=%output_folder%\%output_basename%_stereo.flac"

echo.
echo Convertendo com filtro:
echo !pan_filter!
echo.

ffmpeg -i "!audio_file!" -af "!pan_filter!" -c:a flac "!output_file!"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERRO: Conversao falhou.
    pause >nul
    exit /b
)

echo.
echo ===================================================
echo Conversao concluida com sucesso!
echo Arquivo convertido: !output_file!
echo ===================================================
echo.
pause >nul
exit /b
