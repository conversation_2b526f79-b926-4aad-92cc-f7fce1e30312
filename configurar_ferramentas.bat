@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONFIGURACAO AUTOMATICA DE FERRAMENTAS
echo   Detectando e configurando caminhos corretos
echo ===================================================
echo.

REM Detectar FFmpeg
echo 1. Detectando FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] FFmpeg encontrado no PATH.
    set "ffmpeg_ok=1"
) else (
    echo [AVISO] FFmpeg nao encontrado no PATH.
    set "ffmpeg_ok=0"
)

REM Detectar MediaInfo CLI
echo.
echo 2. Detectando MediaInfo CLI...
set "mediainfo_paths[0]=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mediainfo_paths[1]=C:\Program Files\MediaInfo_CLI\MediaInfo.exe"
set "mediainfo_paths[2]=C:\Program Files (x86)\MediaInfo_CLI\MediaInfo.exe"
set "mediainfo_paths[3]=C:\MediaInfo\MediaInfo.exe"

set "mediainfo_found="
for /l %%i in (0,1,3) do (
    if exist "!mediainfo_paths[%%i]!" (
        set "mediainfo_found=!mediainfo_paths[%%i]!"
        echo [OK] MediaInfo CLI encontrado em: !mediainfo_found!
        goto :mediainfo_ok
    )
)

echo [AVISO] MediaInfo CLI nao encontrado nos locais comuns.
set "mediainfo_found="

:mediainfo_ok

REM Detectar MKVToolNix
echo.
echo 3. Detectando MKVToolNix...
set "mkvextract_paths[0]=C:\MKVToolNix\mkvextract.exe"
set "mkvextract_paths[1]=C:\Program Files\MKVToolNix\mkvextract.exe"
set "mkvextract_paths[2]=C:\Program Files (x86)\MKVToolNix\mkvextract.exe"

set "mkvextract_found="
for /l %%i in (0,1,2) do (
    if exist "!mkvextract_paths[%%i]!" (
        set "mkvextract_found=!mkvextract_paths[%%i]!"
        echo [OK] MKVToolNix encontrado em: !mkvextract_found!
        goto :mkvextract_ok
    )
)

echo [AVISO] MKVToolNix nao encontrado nos locais comuns.
set "mkvextract_found="

:mkvextract_ok

echo.
echo ===================================================
echo   RESULTADO DA DETECCAO
echo ===================================================

if "!ffmpeg_ok!"=="1" (
    echo ✅ FFmpeg: Funcionando
) else (
    echo ❌ FFmpeg: Nao encontrado
)

if not "!mediainfo_found!"=="" (
    echo ✅ MediaInfo CLI: !mediainfo_found!
) else (
    echo ❌ MediaInfo CLI: Nao encontrado
)

if not "!mkvextract_found!"=="" (
    echo ✅ MKVToolNix: !mkvextract_found!
) else (
    echo ❌ MKVToolNix: Nao encontrado
)

echo.
echo ===================================================
echo   CRIANDO SCRIPT PERSONALIZADO
echo ===================================================

REM Criar versao personalizada do script
echo Criando converter_audio_personalizado.bat...

(
echo @echo off
echo setlocal enabledelayedexpansion
echo.
echo echo ===================================================
echo echo   CONVERSOR DE AUDIO PERSONALIZADO PARA KODI
echo echo   Configurado automaticamente para seu sistema
echo echo ===================================================
echo echo.
echo.
echo REM Caminhos detectados automaticamente
echo set "base_folder=%%~dp0"
echo set "input_folder=%%base_folder%%filmes"
echo set "audio_folder=%%base_folder%%audio"
echo set "convertido_folder=%%audio_folder%%\convertido"
echo set "temp_folder=%%audio_folder%%\temp"
echo set "log_folder=%%base_folder%%logs"

if not "!mediainfo_found!"=="" (
    echo set "mediainfo_cli=!mediainfo_found!"
) else (
    echo set "mediainfo_cli=MediaInfo"
)

if not "!mkvextract_found!"=="" (
    echo set "mkvextract_exe=!mkvextract_found!"
) else (
    echo set "mkvextract_exe=mkvextract"
)

echo.
echo REM Criar estrutura de pastas
echo if not exist "%%input_folder%%" mkdir "%%input_folder%%"
echo if not exist "%%audio_folder%%" mkdir "%%audio_folder%%"
echo if not exist "%%convertido_folder%%" mkdir "%%convertido_folder%%"
echo if not exist "%%temp_folder%%" mkdir "%%temp_folder%%"
echo if not exist "%%log_folder%%" mkdir "%%log_folder%%"
echo.
echo echo Estrutura de pastas criada.
echo echo.
echo echo Coloque o arquivo .mkv dentro da pasta: "%%input_folder%%"
echo pause
echo.
echo echo SCRIPT PERSONALIZADO CRIADO!
echo echo Use converter_audio_personalizado.bat
echo pause
) > converter_audio_personalizado.bat

echo [OK] Script personalizado criado: converter_audio_personalizado.bat

echo.
echo ===================================================
echo   INSTRUCOES
echo ===================================================

if "!ffmpeg_ok!"=="0" (
    echo.
    echo ❌ FFMPEG NAO ENCONTRADO:
    echo 1. Baixe em: https://ffmpeg.org/download.html
    echo 2. Extraia para C:\ffmpeg
    echo 3. Adicione C:\ffmpeg\bin ao PATH do sistema
)

if "!mediainfo_found!"=="" (
    echo.
    echo ❌ MEDIAINFO CLI NAO ENCONTRADO:
    echo 1. Baixe em: https://mediaarea.net/MediaInfo
    echo 2. Escolha "CLI" version
    echo 3. Extraia para C:\MediaInfo_CLI_25.04_Windows_x64
)

if "!mkvextract_found!"=="" (
    echo.
    echo ❌ MKVTOOLNIX NAO ENCONTRADO:
    echo 1. Baixe em: https://mkvtoolnix.download/
    echo 2. Instale normalmente
    echo 3. Verifique se foi instalado em C:\MKVToolNix
)

echo.
echo ===================================================
echo   PROXIMOS PASSOS
echo ===================================================

set "tools_count=0"
if "!ffmpeg_ok!"=="1" set /a tools_count+=1
if not "!mediainfo_found!"=="" set /a tools_count+=1
if not "!mkvextract_found!"=="" set /a tools_count+=1

if !tools_count! equ 3 (
    echo.
    echo ✅ TODAS AS FERRAMENTAS ENCONTRADAS!
    echo.
    echo Voce pode usar:
    echo 1. converter_audio_robusto.bat (versao completa)
    echo 2. converter_audio_personalizado.bat (versao otimizada)
    echo.
    echo Recomendacao: Use a versao personalizada!
) else (
    echo.
    echo ⚠️  ALGUMAS FERRAMENTAS ESTAO FALTANDO
    echo.
    echo Instale as ferramentas em falta e execute este script novamente.
    echo Depois use converter_audio_personalizado.bat
)

echo.
pause
