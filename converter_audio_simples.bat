@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONVERSOR DE AUDIO SIMPLES PARA KODI
echo   Aceita espacos e nao fecha inesperadamente
echo ===================================================
echo.

REM Caminhos
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "convertido_folder=%base_folder%audio\convertido"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mkvextract_exe=C:\MKVToolNix\mkvextract.exe"

REM Criar pastas
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"

echo Verificando ferramentas...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] FFmpeg nao encontrado.
    pause
    exit /b 1
)

if not exist "!mkvextract_exe!" (
    echo [ERRO] MKVToolNix nao encontrado.
    pause
    exit /b 1
)

if not exist "!mediainfo_cli!" (
    echo [ERRO] MediaInfo CLI nao encontrado.
    pause
    exit /b 1
)

echo [OK] Todas as ferramentas encontradas.
echo.

echo Coloque o arquivo .mkv na pasta: "%input_folder%"
pause

REM Listar arquivos
echo.
echo Arquivos encontrados:
set "i=0"
for %%F in ("%input_folder%\*.mkv") do (
    set /a i+=1
    set "file_!i!=%%F"
    set "file_name_!i!=%%~nxF"
    echo !i!. %%~nxF
)

if "!i!"=="0" (
    echo Nenhum arquivo encontrado.
    pause
    exit /b 1
)

REM Selecionar arquivo
echo.
set /p "sel=Digite o numero do arquivo (1-!i!): "

REM Validar selecao
set "valid=0"
for /l %%n in (1,1,!i!) do (
    if "!sel!"=="%%n" set "valid=1"
)

if "!valid!"=="0" (
    echo ERRO: Selecao invalida. Execute o script novamente.
    pause
    exit /b 1
)

set "selected_file=!file_%sel%!"
set "selected_filename=!file_name_%sel%!"

echo.
echo Arquivo selecionado: !selected_filename!

REM Nome do filme
echo.
set /p "movie_name=Digite o nome do filme: "

if "!movie_name!"=="" (
    echo ERRO: Nome e obrigatorio. Execute o script novamente.
    pause
    exit /b 1
)

REM Limpar nome (versao simples)
set "movie_base=!movie_name: =_!"
set "movie_base=!movie_base::=-!"
set "movie_base=!movie_base:?=!"
set "movie_base=!movie_base:*=!"
set "movie_base=!movie_base:<=!"
set "movie_base=!movie_base:>=!"
set "movie_base=!movie_base:|=!"
set "movie_base=!movie_base:/=!"
set "movie_base=!movie_base:\=!"
set "movie_base=!movie_base:"=!"
set "movie_base=!movie_base:(=!"
set "movie_base=!movie_base:)=!"

echo Nome do arquivo: !movie_base!

REM Analisar audio
echo.
echo Analisando faixas de audio...
"!mediainfo_cli!" --Inform="Audio;%%ID%%|%%Language%%|%%Format%%|%%Channels%%\n" "!selected_file!" > temp_audio.txt 2>nul

if not exist temp_audio.txt (
    echo ERRO: Falha ao analisar arquivo.
    pause
    exit /b 1
)

echo.
echo Faixas disponiveis:
type temp_audio.txt

REM Selecionar faixa (simples)
echo.
set /p "track_id=Digite o ID da faixa de audio desejada: "

if "!track_id!"=="" (
    echo ERRO: ID e obrigatorio. Execute o script novamente.
    del temp_audio.txt
    pause
    exit /b 1
)

del temp_audio.txt

REM Extrair audio
echo.
echo Extraindo audio...
set "audio_temp=temp_audio.mka"
"!mkvextract_exe!" tracks "!selected_file!" !track_id!:"!audio_temp!" >nul 2>&1

if not exist "!audio_temp!" (
    echo ERRO: Falha na extracao.
    pause
    exit /b 1
)

echo Audio extraido com sucesso.

REM Modo de conversao
echo.
echo Modos de conversao:
echo 1. Dolby Pro Logic II
echo 2. Voz mais clara
echo 3. Ambas as versoes
echo 4. Apenas FLAC
echo.
set /p "mode=Escolha o modo (1-4): "

if "!mode!"=="" set "mode=4"

echo.
echo Convertendo...

REM Converter baseado no modo
if "!mode!"=="1" (
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR" -c:a flac "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" -y >nul 2>&1
    echo Dolby Pro Logic II criado.
)

if "!mode!"=="2" (
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR" -c:a flac "!convertido_folder!\!movie_base!-pt-BR_voz.flac" -y >nul 2>&1
    echo Voz clara criada.
)

if "!mode!"=="3" (
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.325*FL+0.230*FC+0.325*BL|FR=0.325*FR+0.230*FC+0.325*BR" -c:a flac "!convertido_folder!\!movie_base!-pt-BR_dolby.flac" -y >nul 2>&1
    ffmpeg -i "!audio_temp!" -af "pan=stereo|FL=0.3*FL+0.5*FC+0.2*BL|FR=0.3*FR+0.5*FC+0.2*BR" -c:a flac "!convertido_folder!\!movie_base!-pt-BR_voz.flac" -y >nul 2>&1
    echo Ambas as versoes criadas.
)

if "!mode!"=="4" (
    ffmpeg -i "!audio_temp!" -c:a flac "!convertido_folder!\!movie_base!-pt-BR_original.flac" -y >nul 2>&1
    echo FLAC original criado.
)

REM Limpeza
if exist "!audio_temp!" del "!audio_temp!"

echo.
echo ===================================================
echo CONVERSAO CONCLUIDA!
echo.
echo Arquivos criados em: %convertido_folder%
echo.
echo Para usar no Kodi:
echo 1. Coloque os arquivos .flac na mesma pasta do filme
echo 2. Use o mesmo nome base do arquivo de video
echo 3. O Kodi reconhecera automaticamente
echo ===================================================
echo.

pause
