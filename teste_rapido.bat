@echo off
echo ===================================================
echo   TESTE RAPIDO DE FERRAMENTAS
echo ===================================================
echo.

echo 1. Testando FFmpeg...
timeout /t 1 /nobreak >nul
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] FFmpeg funcionando!
) else (
    echo [ERRO] FFmpeg nao encontrado!
)

echo.
echo 2. Testando MediaInfo...
timeout /t 1 /nobreak >nul
echo test | MediaInfo >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] MediaInfo funcionando!
) else (
    echo [AVISO] MediaInfo pode ter problemas
)

echo.
echo 3. Testando MKVToolNix...
timeout /t 1 /nobreak >nul
mkvextract >nul 2>&1
if %errorlevel% neq 9009 (
    echo [OK] MKVToolNix funcionando!
) else (
    echo [ERRO] MKVToolNix nao encontrado!
)

echo.
echo ===================================================
echo RESULTADO: Baseado no que consegui testar,
echo suas ferramentas parecem estar instaladas!
echo ===================================================
pause
