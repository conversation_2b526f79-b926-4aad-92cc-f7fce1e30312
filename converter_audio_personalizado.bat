@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo   CONVERSOR DE AUDIO PERSONALIZADO PARA KODI
echo   Configurado automaticamente para seu sistema
echo ===================================================
echo.

REM Caminhos detectados automaticamente
set "base_folder=%~dp0"
set "input_folder=%base_folder%filmes"
set "audio_folder=%base_folder%audio"
set "convertido_folder=%audio_folder%\convertido"
set "temp_folder=%audio_folder%\temp"
set "log_folder=%base_folder%logs"
set "mediainfo_cli=C:\MediaInfo_CLI_25.04_Windows_x64\MediaInfo.exe"
set "mkvextract_exe=C:\MKVToolNix\mkvextract.exe"

REM Criar estrutura de pastas
if not exist "%input_folder%" mkdir "%input_folder%"
if not exist "%audio_folder%" mkdir "%audio_folder%"
if not exist "%convertido_folder%" mkdir "%convertido_folder%"
if not exist "%temp_folder%" mkdir "%temp_folder%"
if not exist "%log_folder%" mkdir "%log_folder%"

echo Estrutura de pastas criada.
echo.
echo Coloque o arquivo .mkv dentro da pasta: "%input_folder%"
pause

echo SCRIPT PERSONALIZADO CRIADO
echo Use converter_audio_personalizado.bat
pause
